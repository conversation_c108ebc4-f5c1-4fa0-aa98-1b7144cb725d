@import '../../../../styles/cashless-breakpoints.scss';
@import '../../../../styles/cashless-theme.scss';

.pos-tab-container {
  padding: 0rem 1rem 1rem 1rem;
  background-color: #f5f5f5;
}

.top-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
  padding: 1rem;
  position: sticky; /* Make it stick to the top */
  top: 0;
  z-index: 100; /* Ensure it stays on top of other content */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Optional shadow for better visibility */
}

.logo-container {
  width: 290px; /* Adjust as needed */

  img {
    max-width: 100%;
    height: auto;
  }
}

.search-container {
  width: 300px; /* Adjust as needed */
}

// Cross-tab sync status styles
.sync-status-container {
  display: flex;
  align-items: center;
}

.sync-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  transition: all 0.3s ease;

  &.connected {
    background-color: #e8f5e8;
    color: #2e7d32;
    border: 1px solid #c8e6c9;

    .mat-icon {
      color: #4caf50;
      animation: rotate 2s linear infinite;
    }
  }

  &.disconnected {
    background-color: #ffebee;
    color: #c62828;
    border: 1px solid #ffcdd2;

    .mat-icon {
      color: #f44336;
    }
  }

  .sync-text {
    font-weight: 500;
  }

  .last-sync {
    display: block;
    font-size: 0.75rem;
    opacity: 0.8;
    margin-top: 0.25rem;
  }
}

.merchant-sync-status {
  .alert {
    margin-bottom: 0;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    border: none;
    background-color: #e3f2fd;
    color: #1565c0;

    .mat-icon {
      color: #2196f3;
      animation: rotate 2s linear infinite;
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}



// Read-only mode styles for student view
.readonly-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  z-index: 999;
  pointer-events: none; // Allow viewing but prevent all interactions
  cursor: not-allowed;
}

.readonly-banner {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
  z-index: 1001;
  padding: 0.75rem 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

  .readonly-banner-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;

    .mat-icon {
      font-size: 1.5rem;
      width: 1.5rem;
      height: 1.5rem;
    }

    .readonly-text {
      font-weight: 700;
      font-size: 1.1rem;
      letter-spacing: 0.5px;
    }

    .readonly-description {
      font-size: 0.9rem;
      opacity: 0.9;
      margin-left: 1rem;

      @media (max-width: 768px) {
        display: none; // Hide description on mobile for space
      }
    }
  }
}

// Read-only mode container styles
.readonly-mode {
  pointer-events: none !important;
  user-select: none !important;

  // Add visual indication that it's read-only
  &::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 107, 107, 0.05);
    z-index: 998;
    pointer-events: none;
  }

  // Disable all interactive elements
  button,
  input,
  select,
  textarea,
  .mat-form-field,
  .mat-select,
  .mat-option,
  .mat-card,
  .category-tile,
  product-item,
  .cart-item,
  .mat-fab,
  a,
  [role="button"],
  [tabindex] {
    pointer-events: none !important;
    cursor: not-allowed !important;
    opacity: 0.8;

    &:hover {
      transform: none !important;
      box-shadow: none !important;
    }
  }

  // Specific styling for different components
  .menu-item-card {
    opacity: 0.85;

    &:hover {
      transform: none !important;
      box-shadow: none !important;
    }
  }

  .category-tile {
    opacity: 0.85;

    &:hover {
      background-color: transparent !important;
    }
  }

  .readonly-category {
    cursor: not-allowed !important;

    &:hover {
      transform: none !important;
      background-color: transparent !important;
    }
  }

  // Category sync indicator
  .category-sync-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    padding: 0.5rem 1rem;
    background-color: #e3f2fd;
    border-radius: 8px;
    color: #1565c0;
    font-size: 0.9rem;

    .mat-icon {
      margin-right: 0.5rem;
      font-size: 1.2rem;
      width: 1.2rem;
      height: 1.2rem;
      animation: rotate 2s linear infinite;
    }
  }

  // Cart sync indicator
  .cart-sync-indicator {
    display: flex;
    align-items: center;
    margin-left: 1rem;
    font-size: 0.8rem;
    color: #2196f3;
    font-weight: 400;

    .mat-icon {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
      margin-right: 0.25rem;

      &.syncing {
        animation: rotate 1s linear infinite;
        color: #4caf50; // Green when actively syncing
      }

      &:not(.syncing) {
        animation: none;
      }
    }

    .sync-text {
      white-space: nowrap;

      @media (max-width: 768px) {
        display: none; // Hide text on mobile, keep icon
      }
    }
  }

  .shopping-cart-sidebar {
    opacity: 0.9;

    .cart-item {
      .item-actions button {
        display: none; // Hide delete buttons in cart
      }
    }

    .place-order-btn {
      opacity: 0.6;
    }
  }

  .mobile-place-order {
    opacity: 0.6;
  }

  // Readonly indicators
  .readonly-indicator {
    display: flex;
    align-items: center;
    color: #666;
    font-size: 0.9rem;

    .mat-icon {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
      margin-right: 0.25rem;
    }
  }

  .readonly-order-info {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem;
    background-color: #f5f5f5;
    border-radius: 4px;
    color: #666;
    font-size: 0.9rem;

    .mat-icon {
      margin-right: 0.5rem;
      font-size: 1.2rem;
      width: 1.2rem;
      height: 1.2rem;
    }
  }
}

// Adjust top margin for content when readonly banner is shown
.readonly-mode .top-header {
  margin-top: 60px; // Account for readonly banner height
}

.readonly-mode .pos-tab-container {
  margin-top: 20px; // Additional spacing
}

// Student view popup styles
.student-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1002; // Higher than readonly banner
  display: flex;
  align-items: center;
  justify-content: center;
}

.student-popup-content {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  max-width: 400px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;

  .popup-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;

    .merchant-indicator {
      background-color: #2196f3;
      color: white;
      padding: 0.25rem 0.75rem;
      border-radius: 12px;
      font-size: 0.8rem;
      margin-right: 1rem;
    }

    h3 {
      margin: 0;
      color: #333;
    }
  }

  .popup-item-image {
    width: 100%;
    max-width: 200px;
    height: auto;
    border-radius: 8px;
    margin-bottom: 1rem;
  }

  .popup-item-details {
    .item-name {
      font-size: 1.2rem;
      font-weight: 600;
      color: #333;
      margin-bottom: 0.5rem;
    }

    .item-price {
      font-size: 1.1rem;
      color: #2196f3;
      font-weight: 500;
      margin-bottom: 1rem;
    }

    .item-description {
      color: #666;
      line-height: 1.5;
    }
  }

  .readonly-notice {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 0.75rem;
    margin-top: 1rem;
    text-align: center;
    color: #856404;
    font-size: 0.9rem;
  }
}

.pos-header {
  text-align: center;
  margin-bottom: 2rem;

  .pos-title {
    font-size: 2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
  }

  .pos-subtitle {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 0;
  }
}

.filters-card {
  margin-bottom: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .mat-card-content {
    padding: 1.5rem;
  }
}

.ordering-form {
  .full-width {
    width: 100%;
  }

  .mat-form-field {
    margin-bottom: 0;
  }
}

.categories-container {
  margin-bottom: 0rem;
  margin-left: 0px;
}

.categories-list {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
  padding: 3px;
  margin: 0;
  list-style: none;
  gap: 0.75rem;

  &.scrolling-horizontal-wrapper {
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }
}

.category-item {
  flex: 0 0 auto;
  margin: 0;
}

.category-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 1rem 0;
  color: #333;
}

.menu-item-card {
  height: 100%;
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

.no-menu-card,
.no-items-card,
.waiting-card {
  text-align: center;
  padding: 2rem;

  .no-menu-message,
  .no-items-message {
    font-size: 1.1rem;
    color: #666;
    margin: 0;
  }
}

.waiting-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2px dashed #dee2e6;

  .waiting-icon {
    font-size: 4rem;
    width: 4rem;
    height: 4rem;
    color: #6c757d;
    margin-bottom: 1rem;
  }

  .waiting-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
  }

  .waiting-message {
    color: #6c757d;
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
  }

  .sync-status-inline {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background-color: #e3f2fd;
    color: #1565c0;
    border-radius: 20px;
    font-size: 0.9rem;

    .sync-icon {
      font-size: 1.2rem;
      width: 1.2rem;
      height: 1.2rem;
      animation: rotate 2s linear infinite;
    }
  }
}

.shopping-cart-sidebar {
  position: sticky;
  top: 2rem;

  .cart-card {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    .mat-card-header {
      padding-bottom: 0;

      .mat-card-title {
        font-size: 1.2rem;
        font-weight: 600;
      }
    }
  }
}

.empty-cart {
  text-align: center;
  padding: 2rem 1rem;
  color: #999;
}

.cart-items {
  .cart-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 0.75rem 0;
    border-bottom: 1px solid #eee;

    &:last-child {
      border-bottom: none;
    }

    .item-info {
      flex: 1;

      .item-name {
        font-size: 0.9rem;
        font-weight: 500;
        margin: 0 0 0.25rem 0;
        color: #333;
      }

      .item-details {
        font-size: 0.8rem;
        color: #666;
        margin: 0;
      }
    }

    .item-actions {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      .item-price {
        font-weight: 500;
        color: #333;
      }

      button {
        min-width: auto;
        width: 32px;
        height: 32px;

        .mat-icon {
          font-size: 18px;
          width: 18px;
          height: 18px;
        }
      }
    }
  }
}

.cart-total {
  text-align: center;
  padding: 0.5rem 0;
  border-top: 2px solid #eee;
  margin-top: 1rem;

  h5 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
  }
}

// Payment Methods Styling
.payment-section {
  padding: 1rem !important;

  .payment-methods-container {
    width: 100%;

    .payment-title {
      font-size: 1.1rem;
      font-weight: 600;
      margin: 0 0 1rem 0;
      color: #333;
      text-align: left;
    }

    .payment-btn {
      width: 100%;
      margin-bottom: 0.5rem;
      padding: 0.75rem 1rem;
      border-radius: 8px;
      font-size: 1.0rem;
      font-weight: 500;
      text-align: left;
      display: flex;
      align-items: center;
      gap: 0.75rem;
      transition: all 0.3s ease;

      .mat-icon {
        font-size: 1.2rem;
        width: 1.2rem;
        height: 1.2rem;
      }

      span {
        flex: 1;
        text-align: left;
      }

      &.primary-payment {
        background: linear-gradient(135deg, #ff8c00 0%, #ff6b47 100%);
        color: white;
        border: none;
        font-weight: 600;

        &:hover:not(:disabled) {
          background: linear-gradient(135deg, #ff7a00 0%, #ff5a37 100%);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(255, 140, 0, 0.3);
        }

        &.selected {
          box-shadow: 0 0 0 3px rgba(255, 140, 0, 0.3);
        }
      }

      &.secondary-payment {
        background-color: #f8f9fa;
        color: #495057;
        border: 2px solid #dee2e6;

        &:hover:not(:disabled) {
          background-color: #e9ecef;
          border-color: #ced4da;
          transform: translateY(-1px);
        }

        &.selected {
          background-color: #e7f3ff;
          border-color: #007bff;
          color: #0056b3;
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
          background-color: #f8f9fa;
          color: #6c757d;

          &:hover {
            transform: none;
            background-color: #f8f9fa;
            border-color: #dee2e6;
          }
        }

        // Specific styling for different payment methods
        &[data-payment="stripe"] {
          .mat-icon {
            color: #635bff;
          }
        }

        &[data-payment="cash"] {
          .mat-icon {
            color: #28a745;
          }
        }

        &[data-payment="applepay"] {
          .mat-icon {
            color: #000;
          }
        }

        &[data-payment="visa"] {
          .mat-icon {
            color: #1a1f71;
          }
        }
      }
    }

    .secondary-payment-row {
      display: flex;
      gap: 0.5rem;
      margin-bottom: 0.5rem;

      .payment-btn {
        flex: 1;
        margin-bottom: 0;
      }
    }

    .final-place-order-btn {
      width: 100%;
      padding: 1rem;
      font-size: 1.1rem;
      font-weight: 600;
      margin-top: 1rem;
      background: linear-gradient(135deg, #ff8c00 0%, #ff6b47 100%);
      color: white;
      border: none;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        background: linear-gradient(135deg, #ff7a00 0%, #ff5a37 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255, 140, 0, 0.4);
      }
    }
  }
}

// Legacy place order button (fallback)
.place-order-btn {
  width: 100%;
  padding: 0.75rem;
  font-size: 1rem;
  font-weight: 500;
}

.mobile-place-order {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 1000;

  .mobile-payment-container {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.5rem;

    .mobile-payment-indicator {
      background-color: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      padding: 0.5rem 0.75rem;
      border-radius: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 0.8rem;
      color: #333;
      max-width: 200px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background-color: rgba(255, 255, 255, 1);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      }

      .mat-icon {
        font-size: 1rem;
        width: 1rem;
        height: 1rem;
        color: #ff8c00;

        &.expand-icon {
          color: #666;
          margin-left: auto;
        }
      }

      span {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
      }
    }
  }

  .fab-place-order {
    width: 56px;
    height: 56px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    background: linear-gradient(135deg, #ff8c00 0%, #ff6b47 100%);

    &:hover {
      background: linear-gradient(135deg, #ff7a00 0%, #ff5a37 100%);
    }
  }
}

// Mobile readonly indicator
.mobile-readonly-indicator {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 1000;

  .readonly-fab {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 64px;
    height: 64px;
    background-color: #666;
    color: white;
    border-radius: 50%;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    font-size: 0.7rem;
    text-align: center;

    .mat-icon {
      font-size: 1.2rem;
      width: 1.2rem;
      height: 1.2rem;
      margin-bottom: 0.1rem;
    }

    .cart-total {
      font-weight: 600;
      font-size: 0.65rem;
    }
  }
}

// Responsive adjustments
@media (max-width: $breakpoint-md) {
  .pos-tab-container {
    padding: 0.5rem;
  }

  .pos-header {
    margin-bottom: 1rem;

    .pos-title {
      font-size: 1.5rem;
    }
  }

  .filters-card {
    margin-bottom: 1rem;

    .mat-card-content {
      padding: 1rem;
    }
  }

  .shopping-cart-sidebar {
    position: static;
    margin-top: 2rem;
  }

  // Mobile payment methods adjustments
  .payment-section {
    .payment-methods-container {
      .payment-btn {
        font-size: 0.8rem;
        padding: 0.6rem 0.8rem;

        .mat-icon {
          font-size: 1rem;
          width: 1rem;
          height: 1rem;
        }
      }

      .secondary-payment-row {
        flex-direction: column;
        gap: 0.25rem;

        .payment-btn {
          width: 100%;
        }
      }

      .final-place-order-btn {
        font-size: 1rem;
        padding: 0.8rem;
      }
    }
  }

  .category-title {
    font-size: 1.3rem;
    margin: 0.5rem 0;
  }
}

@media (max-width: $breakpoint-sm) {
  .ordering-form {
    .row > div {
      margin-bottom: 1rem;
    }
  }

  .menu-item-card {
    margin-bottom: 1rem;
  }
}

// Cart sync notification
.cart-sync-notification {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  margin-bottom: 1rem;
  background-color: #e8f5e8;
  border-radius: 4px;
  color: #2e7d32;
  font-size: 0.9rem;
  border-left: 3px solid #4caf50;

  .mat-icon {
    margin-right: 0.5rem;
    font-size: 1.1rem;
    width: 1.1rem;
    height: 1.1rem;
    animation: rotate 1s linear infinite;
  }
}

// Mobile Payment Selector
.mobile-payment-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1001;
  display: flex;
  align-items: flex-end;

  .mobile-payment-selector {
    width: 100%;
    background-color: white;
    border-radius: 16px 16px 0 0;
    padding: 1rem;
    max-height: 70vh;
    overflow-y: auto;
    animation: slideUp 0.3s ease-out;

    .mobile-payment-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 1rem;
      padding-bottom: 0.5rem;
      border-bottom: 1px solid #eee;

      h6 {
        margin: 0;
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
      }

      button {
        color: #666;
      }
    }

    .mobile-payment-options {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;

      .mobile-payment-option {
        width: 100%;
        padding: 1rem;
        border-radius: 8px;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        text-align: left;
        border: 2px solid #dee2e6;
        background-color: #f8f9fa;
        color: #495057;
        transition: all 0.3s ease;

        &.primary {
          background: linear-gradient(135deg, #ff8c00 0%, #ff6b47 100%);
          color: white;
          border-color: #ff8c00;

          .mat-icon {
            color: white;
          }
        }

        &.selected {
          border-color: #007bff;
          background-color: #e7f3ff;
          color: #0056b3;

          &.primary {
            border-color: #ff6b47;
            box-shadow: 0 0 0 2px rgba(255, 140, 0, 0.3);
          }
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .mat-icon {
          font-size: 1.2rem;
          width: 1.2rem;
          height: 1.2rem;

          &.check-icon {
            margin-left: auto;
            color: #28a745;
          }
        }

        span {
          flex: 1;
          font-size: 0.9rem;
          font-weight: 500;
        }
      }
    }
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}


@media (min-width: 768px) {
  .payment-methods-container {
    .payment-btn {
      font-size: 1rem;
      padding: 0.75rem 1rem;

      .mat-icon {
        font-size: 1.2rem;
      }
    }

    .secondary-payment-row {
      flex-direction: row;
      gap: 1rem;

      .payment-btn {
        flex: 1;
      }
    }

    .final-place-order-btn {
      font-size: 1.1rem;
    }
  }
}

@media (min-width: 1024px) {
  .payment-methods-container {
    max-width: 700px;
    margin: auto;
  }
}


::ng-deep .mdc-button__label{
  font-size: 0.8rem!important;
}

::ng-deep .mb-3 {
  margin-bottom: 0.5rem!important;
}


::ng-deep .mat-mdc-card-content:last-child {
  padding-bottom: 0px!important;
}