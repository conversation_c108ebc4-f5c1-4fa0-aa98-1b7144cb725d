import { Component, OnInit, OnDestroy, Input, Output, EventEmitter } from '@angular/core';
import { FormControl } from '@angular/forms';
import { Observable, Subscription, of } from 'rxjs';
import { debounceTime, distinctUntilChanged, switchMap, startWith, map } from 'rxjs/operators';
import { UserCashless, ArrayFilter } from '../../../sharedModels';
import { UserService, SpinnerService } from '../../../sharedServices';

@Component({
  selector: 'student-search-dropdown',
  templateUrl: './student-search-dropdown.component.html',
  styleUrls: ['./student-search-dropdown.component.scss'],
})
export class StudentSearchDropdownComponent implements OnInit, OnDestroy {
  @Input() label: string = '';
  @Input() placeholder: string = 'Search for a student...';
  @Input() schoolId: number = 52243;
  @Output() studentSelected = new EventEmitter<UserCashless | null>();

  searchControl = new FormControl();
  filteredStudents: Observable<UserCashless[]>;
  students: UserCashless[] = [];
  selectedStudent: UserCashless | null = null;
  isLoading = false;

  private searchSubscription: Subscription;

  constructor(
    private userService: UserService,
    private spinnerService: SpinnerService
  ) {}

  ngOnInit(): void {
    this.setupSearch();
  }

  ngOnDestroy(): void {
    if (this.searchSubscription) {
      this.searchSubscription.unsubscribe();
    }
  }

  private setupSearch(): void {
    this.filteredStudents = this.searchControl.valueChanges.pipe(
      startWith(''),
      debounceTime(300),
      distinctUntilChanged(),
      switchMap(value => {
        if (typeof value === 'string' && value.length >= 2) {
          return this.searchStudents(value);
        } else {
          return of([]);
        }
      })
    );
  }

  private searchStudents(searchTerm: string): Observable<UserCashless[]> {
    this.isLoading = true;

    const filters: ArrayFilter = {
      Filter: searchTerm,
      NumberRows: 50,
      PageIndex: 0,
      FilterId: 0,
      SortBy: '',
      SortDirection: '',
      MultipleFilterId: this.schoolId.toString(),
      Role: '',
      MerchantId: 0
    };

    return this.userService.GetUsersWithFilterAPI(filters).pipe(
      map((response: any) => {
        this.isLoading = false;
        return response.Users || [];
      })
    );
  }

  onStudentSelected(student: UserCashless): void {
    if (student) {
      this.selectedStudent = student;
      this.searchControl.setValue(student, { emitEvent: false }); // <- important!
      this.studentSelected.emit(student);
    }
  }

  displayFn(student: UserCashless | null): string {
    return student && student.FirstName && student.Lastname && student.ClassName
      ? `${student.FirstName} ${student.Lastname} - ${student.ClassName}`
      : '';
  }

  clearSelection(): void {
    this.selectedStudent = null;
    this.searchControl.setValue('', { emitEvent: false });
    this.studentSelected.emit(null);
  }
}
